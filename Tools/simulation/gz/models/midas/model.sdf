<?xml version="1.0"?>
<sdf version="1.6">
  <model name="midas">
    <pose>0 0 0.0100 0 0 0</pose>
    <self_collide>false</self_collide>
    <static>false</static>

    <link name="base_link">
      <pose>0 0 0 0 0 0</pose>
      <inertial>
        <pose>0 0 -0.0075 0 0 0</pose> <!-- COM_offset -->
        <mass>2.5000</mass> <!-- m_mb -->
        <inertia>
          <ixx>0.0144</ixx> <!-- lbx -->
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.0434</iyy> <!-- lby -->
          <iyz>0</iyz>
          <izz>0.0410</izz> <!-- lbz -->
        </inertia>
      </inertial>
      <collision name="base_link_inertia_collision">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <box>
            <size>0.4100 0.1700 0.2000</size> <!-- L_mb, W_mb, D_mb -->
          </box>
        </geometry>
      </collision>
      <visual name="base_link_inertia_visual">
        <pose>0 0 0.0100 1.55 0 1.50</pose>
        <geometry>
          <mesh>
            <scale>0.001 0.001 0.001</scale>
            <uri>model://midas/meshes/base_model.stl</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>

      <!-- Sensors -->
      <sensor name="air_pressure_sensor" type="air_pressure">
        <always_on>1</always_on>
        <update_rate>50</update_rate>
        <air_pressure>
          <pressure>
            <noise type="gaussian">
              <mean>0</mean>
              <stddev>3</stddev>
            </noise>
          </pressure>
        </air_pressure>
      </sensor>

      <sensor name="magnetometer_sensor" type="magnetometer">
        <always_on>1</always_on>
        <update_rate>100</update_rate>
        <magnetometer>
          <x>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </x>
          <y>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </y>
          <z>
            <noise type="gaussian">
              <stddev>0.0001</stddev>
            </noise>
          </z>
        </magnetometer>
      </sensor>

      <sensor name="imu_sensor" type="imu">
        <always_on>1</always_on>
        <update_rate>250</update_rate>
        <imu>
          <angular_velocity>
            <x>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.0008726646</stddev>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.0008726646</stddev>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.0008726646</stddev>
              </noise>
            </z>
          </angular_velocity>
          <linear_acceleration>
            <x>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.00637</stddev>
              </noise>
            </x>
            <y>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.00637</stddev>
              </noise>
            </y>
            <z>
              <noise type="gaussian">
                <mean>0.0</mean>
                <stddev>0.00686</stddev>
              </noise>
            </z>
          </linear_acceleration>
        </imu>
      </sensor>

      <sensor name="navsat_sensor" type="navsat">
        <always_on>1</always_on>
        <update_rate>30</update_rate>
      </sensor>
    </link>

    <!-- Motor assemblies -->
    <!-- Rear Right Motor Assembly (DL_loc) -->
    <link name="rear_right_motor_assembly">
      <pose>0 0 0.0490 0 0 0</pose>
      <inertial>
        <mass>0.5000</mass> <!-- m_propulsion -->
        <inertia>
          <ixx>0.006667</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.006667</iyy>
          <iyz>0</iyz>
          <izz>0.003333</izz>
        </inertia>
      </inertial>
      <visual name="rear_right_motor_assembly_visual">
        <pose>-0.2600 -0.3450 0.0490 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/TiltModule.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Red</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
    </link>
    <joint name="rear_right_tilt_joint" type="fixed">
      <child>rear_right_motor_assembly</child>
      <parent>base_link</parent>
    </joint>

    <!-- Rear Left Motor Assembly (DR_loc) -->
    <link name="rear_left_motor_assembly">
      <pose>0 0 0.0490 0 0 0</pose>
      <inertial>
        <mass>0.5000</mass>
        <inertia>
          <ixx>0.006667</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.006667</iyy>
          <iyz>0</iyz>
          <izz>0.003333</izz>
        </inertia>
      </inertial>
      <visual name="rear_left_motor_assembly_visual">
        <pose>-0.2600 0.3450 0.0490 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/TiltModule.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Green</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
    </link>
    <joint name="rear_left_tilt_joint" type="fixed">
      <child>rear_left_motor_assembly</child>
      <parent>base_link</parent>
    </joint>

    <!-- Front Right Motor Assembly (UL_loc) -->
    <link name="front_right_motor_assembly">
      <pose>0 0 0.0490 0 0 0</pose>
      <inertial>
        <mass>0.5000</mass>
        <inertia>
          <ixx>0.006667</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.006667</iyy>
          <iyz>0</iyz>
          <izz>0.003333</izz>
        </inertia>
      </inertial>
      <visual name="front_right_motor_assembly_visual">
        <pose>0.2450 -0.3450 0.0490 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/TiltModule.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Blue</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
    </link>
    <joint name="front_right_tilt_joint" type="fixed">
      <child>front_right_motor_assembly</child>
      <parent>base_link</parent>
    </joint>

    <!-- Front Left Motor Assembly (UR_loc) -->
    <link name="front_left_motor_assembly">
      <pose>0.2450 0.3450 0.0490 0 0 0</pose>
      <inertial>
        <mass>0.5000</mass>
        <inertia>
          <ixx>0.006667</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>0.006667</iyy>
          <iyz>0</iyz>
          <izz>0.003333</izz>
        </inertia>
      </inertial>
      <visual name="front_left_motor_assembly_visual">
        <pose>0 0 0 0 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/TiltModule.dae</uri>
          </mesh>
        </geometry>
        <material>
          <script>
            <name>Gazebo/Yellow</name>
            <uri>file://media/materials/scripts/gazebo.material</uri>
          </script>
        </material>
      </visual>
      <gravity>1</gravity>
    </link>
    <joint name="front_left_tilt_joint" type="fixed">
      <child>front_left_motor_assembly</child>
      <parent>base_link</parent>
    </joint>

    <!-- Propellers -->
    <!-- Rear Right Propeller -->
    <link name="rear_right_propeller">
      <pose>0 0 0.025 0 0 0</pose>
      <inertial>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>4.17041e-05</iyy>
          <iyz>0</iyz>
          <izz>4.26041e-05</izz>
        </inertia>
      </inertial>
      <collision name="rear_right_propeller_collision">
        <geometry>
          <cylinder>
            <radius>0.1778</radius> <!-- prop_Diameter/2 = 0.3556/2 -->
            <length>0.01</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="rear_right_propeller_visual">
        <pose>0 0 0 1.5708 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/13x5prop.stl</uri>
          </mesh>
        </geometry>
        <material>
          <diffuse>1 0 0 1</diffuse>
        </material>
      </visual>
    </link>
    <joint name="rear_right_propeller_joint" type="revolute">
      <child>rear_right_propeller</child>
      <parent>rear_right_motor_assembly</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>

    <!-- Rear Left Propeller -->
    <link name="rear_left_propeller">
      <pose>0 0 0.025 0 0 0</pose>
      <inertial>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>4.17041e-05</iyy>
          <iyz>0</iyz>
          <izz>4.26041e-05</izz>
        </inertia>
      </inertial>
      <collision name="rear_left_propeller_collision">
        <geometry>
          <cylinder>
            <radius>0.1778</radius>
            <length>0.01</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="rear_left_propeller_visual">
        <pose>0 0 0 1.5708 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/13x5prop.stl</uri>
          </mesh>
        </geometry>
        <material>
          <diffuse>0 1 0 1</diffuse>
        </material>
      </visual>
    </link>
    <joint name="rear_left_propeller_joint" type="revolute">
      <child>rear_left_propeller</child>
      <parent>rear_left_motor_assembly</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>

    <!-- Front Right Propeller -->
    <link name="front_right_propeller">
      <pose>0 0 0.025 0 0 0</pose>
      <inertial>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>4.17041e-05</iyy>
          <iyz>0</iyz>
          <izz>4.26041e-05</izz>
        </inertia>
      </inertial>
      <collision name="front_right_propeller_collision">
        <geometry>
          <cylinder>
            <radius>0.1778</radius>
            <length>0.01</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="front_right_propeller_visual">
        <pose>0 0 0 1.5708 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/13x5prop.stl</uri>
          </mesh>
        </geometry>
        <material>
          <diffuse>0 0 1 1</diffuse>
        </material>
      </visual>
    </link>
    <joint name="front_right_propeller_joint" type="revolute">
      <child>front_right_propeller</child>
      <parent>front_right_motor_assembly</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>

    <!-- Front Left Propeller -->
    <link name="front_left_propeller">
      <pose>0 0 0.025 0 0 0</pose>
      <inertial>
        <mass>0.005</mass>
        <inertia>
          <ixx>9.75e-07</ixx>
          <ixy>0</ixy>
          <ixz>0</ixz>
          <iyy>4.17041e-05</iyy>
          <iyz>0</iyz>
          <izz>4.26041e-05</izz>
        </inertia>
      </inertial>
      <collision name="front_left_propeller_collision">
        <geometry>
          <cylinder>
            <radius>0.1778</radius>
            <length>0.01</length>
          </cylinder>
        </geometry>
      </collision>
      <visual name="front_left_propeller_visual">
        <pose>0 0 0 1.5708 0 0</pose>
        <geometry>
          <mesh>
            <scale>1 1 1</scale>
            <uri>model://midas/meshes/13x5prop.stl</uri>
          </mesh>
        </geometry>
        <material>
          <diffuse>1 1 0 1</diffuse>
        </material>
      </visual>
    </link>
    <joint name="front_left_propeller_joint" type="revolute">
      <child>front_left_propeller</child>
      <parent>front_left_motor_assembly</parent>
      <axis>
        <xyz>0 0 1</xyz>
        <limit>
          <lower>-1e+16</lower>
          <upper>1e+16</upper>
        </limit>
        <use_parent_model_frame>1</use_parent_model_frame>
      </axis>
    </joint>

    <!-- Motor plugins -->
    <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
      <jointName>rear_right_propeller_joint</jointName>
      <linkName>rear_right_propeller</linkName>
      <turningDirection>ccw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>2211.0</maxRotVelocity> <!-- sqrt(19.62/4.0123e-6) -->
      <motorConstant>4.0123e-06</motorConstant> <!-- prop_Kf -->
      <momentConstant>8.0246e-08</momentConstant> <!-- prop_Ktau -->
      <commandSubTopic>command/motor_speed</commandSubTopic>
      <motorNumber>0</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <motorType>velocity</motorType>
    </plugin>

    <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
      <jointName>rear_left_propeller_joint</jointName>
      <linkName>rear_left_propeller</linkName>
      <turningDirection>cw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>2211.0</maxRotVelocity>
      <motorConstant>4.0123e-06</motorConstant>
      <momentConstant>8.0246e-08</momentConstant>
      <commandSubTopic>command/motor_speed</commandSubTopic>
      <motorNumber>1</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <motorType>velocity</motorType>
    </plugin>

    <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
      <jointName>front_right_propeller_joint</jointName>
      <linkName>front_right_propeller</linkName>
      <turningDirection>cw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>2211.0</maxRotVelocity>
      <motorConstant>4.0123e-06</motorConstant>
      <momentConstant>8.0246e-08</momentConstant>
      <commandSubTopic>command/motor_speed</commandSubTopic>
      <motorNumber>2</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <motorType>velocity</motorType>
    </plugin>

    <plugin filename="gz-sim-multicopter-motor-model-system" name="gz::sim::systems::MulticopterMotorModel">
      <jointName>front_left_propeller_joint</jointName>
      <linkName>front_left_propeller</linkName>
      <turningDirection>ccw</turningDirection>
      <timeConstantUp>0.0125</timeConstantUp>
      <timeConstantDown>0.025</timeConstantDown>
      <maxRotVelocity>2211.0</maxRotVelocity>
      <motorConstant>4.0123e-06</motorConstant>
      <momentConstant>8.0246e-08</momentConstant>
      <commandSubTopic>command/motor_speed</commandSubTopic>
      <motorNumber>3</motorNumber>
      <rotorDragCoefficient>8.06428e-05</rotorDragCoefficient>
      <rollingMomentCoefficient>1e-06</rollingMomentCoefficient>
      <rotorVelocitySlowdownSim>10</rotorVelocitySlowdownSim>
      <motorType>velocity</motorType>
    </plugin>
  </model>
</sdf>
